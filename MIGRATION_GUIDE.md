# Google Gemini to DeepSeek Migration Guide

This document outlines the comprehensive migration from Google Gemini to DeepSeek (via OpenRouter) that has been completed for your codebase.

## 🔄 Migration Summary

### What Was Changed

1. **Dependencies**
   - ❌ Removed: `langchain-google-genai>=2.1.5`
   - ❌ Removed: `google-generativeai>=0.8.5`
   - ✅ Added: `openai>=1.0.0`

2. **API Configuration**
   - ❌ Old: `GOOGLE_API_KEY` environment variable
   - ✅ New: `OPENROUTER_API_KEY` environment variable

3. **Model Imports**
   - ❌ Old: `from langchain_google_genai import ChatGoogleGenerativeAI`
   - ✅ New: `from deepseek_integration import ChatDeepSeek`

4. **Model Initialization**
   - ❌ Old: `ChatGoogleGenerativeAI(model="gemini-2.5-flash", temperature=0.5)`
   - ✅ New: `ChatDeepSeek(model="deepseek/deepseek-chat:free", temperature=0.5)`

5. **Integration Method**
   - ❌ Old: Direct Google Gemini API
   - ✅ New: DeepSeek via OpenRouter API using OpenAI client library

## 🚀 Getting Started

### 1. Set Up Virtual Environment

The migration includes a pre-configured virtual environment named `ai-agent`:

```bash
# Activate the environment
source ai-agent/bin/activate

# Verify installation
python test_gemini_migration.py
```

### 2. Get OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/keys)
2. Create a new API key
3. Set the environment variable:
   ```bash
   export OPENROUTER_API_KEY="your-api-key-here"
   ```

### 3. Use Jupyter with the New Environment

The virtual environment is registered as a Jupyter kernel:

```bash
# Start Jupyter
jupyter lab

# Or Jupyter Notebook
jupyter notebook
```

In Jupyter, select the "Python (ai-agent)" kernel for your notebooks.

## 📝 Updated Files

### Core Files
- `Build_a_Planning_Agent_for_Deep_Research_&_Structured_Report_Generation.ipynb` - Updated notebook
- `requirements.txt` - Updated dependencies
- `deepseek_integration.py` - DeepSeek integration module
- `test_deepseek_integration.py` - Integration verification script

### Key Changes in Notebook

1. **Installation Cell**
   ```python
   # Old
   !pip install langchain-google-genai>=2.1.5
   !pip install google-generativeai>=0.8.5

   # New
   !pip install openai>=1.0.0
   ```

2. **API Key Setup**
   ```python
   # Old
   GEMINI_API_KEY = getpass('Enter Google Gemini API Key: ')
   os.environ['GOOGLE_API_KEY'] = GEMINI_API_KEY

   # New
   OPENROUTER_API_KEY = getpass('Enter OpenRouter API Key: ')
   os.environ['OPENROUTER_API_KEY'] = OPENROUTER_API_KEY
   ```

3. **Model Usage**
   ```python
   # Old
   from langchain_google_genai import ChatGoogleGenerativeAI
   llm = ChatGoogleGenerativeAI(model="gemini-2.5-flash", temperature=0.5)

   # New
   from deepseek_integration import ChatDeepSeek
   llm = ChatDeepSeek(model="deepseek/deepseek-chat:free", temperature=0.5)
   ```

## 🔧 Available DeepSeek Models via OpenRouter

- `deepseek/deepseek-chat:free` - Free tier DeepSeek model (recommended for testing)
- `deepseek/deepseek-chat` - Full DeepSeek model (paid)
- `deepseek/deepseek-coder` - Code-specialized DeepSeek model

## ⚠️ Important Notes

1. **Token Counting**: The integration maintains the simple estimation function (4 chars ≈ 1 token). For production use, consider implementing more accurate token counting for DeepSeek.

2. **Model Capabilities**: DeepSeek models may have different capabilities and response formats compared to Gemini models. Test thoroughly.

3. **Rate Limits**: OpenRouter and DeepSeek have different rate limits than Google Gemini. Monitor your usage accordingly.

4. **Cost**: DeepSeek via OpenRouter has different pricing than Google Gemini. The free tier is available for testing.

5. **OpenRouter Features**: The integration supports optional HTTP-Referer and X-Title headers for OpenRouter rankings.

## 🧪 Testing

Run the integration test to verify everything works:

```bash
source ai-agent/bin/activate
python test_deepseek_integration.py
```

## 📚 Additional Resources

- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [DeepSeek Model Documentation](https://platform.deepseek.com/api-docs/)
- [OpenAI Python Client Library](https://github.com/openai/openai-python)

## 🆘 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you have the `openai` package installed
2. **API Key Issues**: Verify `OPENROUTER_API_KEY` is set correctly
3. **Model Errors**: Check that you have access to the DeepSeek model via OpenRouter
4. **Module Not Found**: Ensure `deepseek_integration.py` is in your Python path

### Getting Help

If you encounter issues:
1. Check the test script output: `python test_deepseek_integration.py`
2. Verify your API key has the necessary permissions on OpenRouter
3. Review the OpenRouter documentation for troubleshooting

---

✅ **Migration Complete!** Your codebase now uses DeepSeek via OpenRouter instead of Google Gemini.
