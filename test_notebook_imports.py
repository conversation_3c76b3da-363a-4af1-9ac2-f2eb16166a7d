#!/usr/bin/env python3
"""
Test script to verify all notebook imports work correctly in the ai-agent environment.
This simulates the imports that will be used in the Jupyter notebook.
"""

import sys
import os

def test_notebook_imports():
    """Test all the imports that will be used in the notebook."""
    print("🧪 Testing Notebook Imports in ai-agent Environment")
    print("=" * 60)
    
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print()
    
    # Test 1: Basic imports
    print("1. Testing basic imports...")
    try:
        import os
        from getpass import getpass
        print("   ✅ Basic imports successful")
    except Exception as e:
        print(f"   ❌ Basic imports failed: {e}")
        return False
    
    # Test 2: OpenAI import
    print("2. Testing OpenAI import...")
    try:
        from openai import OpenAI
        print("   ✅ OpenAI import successful")
    except Exception as e:
        print(f"   ❌ OpenAI import failed: {e}")
        return False
    
    # Test 3: DeepSeek integration import
    print("3. Testing DeepSeek integration import...")
    try:
        from deepseek_integration import ChatDeepSeek
        print("   ✅ DeepSeek integration import successful")
    except Exception as e:
        print(f"   ❌ DeepSeek integration import failed: {e}")
        return False
    
    # Test 4: LangChain imports
    print("4. Testing LangChain imports...")
    try:
        from langchain_core.messages import HumanMessage, SystemMessage
        print("   ✅ LangChain core imports successful")
    except Exception as e:
        print(f"   ❌ LangChain core imports failed: {e}")
        return False
    
    # Test 5: Model initialization (without API key)
    print("5. Testing model initialization...")
    try:
        # Set a dummy API key for testing
        os.environ['OPENROUTER_API_KEY'] = 'test-key-for-initialization'
        
        llm = ChatDeepSeek(
            model="deepseek/deepseek-chat:free",
            temperature=0.5
        )
        print("   ✅ Model initialization successful")
        
        # Clean up
        del os.environ['OPENROUTER_API_KEY']
        
    except Exception as e:
        print(f"   ❌ Model initialization failed: {e}")
        return False
    
    # Test 6: Compatibility alias
    print("6. Testing compatibility alias...")
    try:
        from deepseek_integration import ChatGoogleGenerativeAI
        print("   ✅ Compatibility alias import successful")
    except Exception as e:
        print(f"   ❌ Compatibility alias failed: {e}")
        return False
    
    print()
    print("=" * 60)
    print("🎉 All notebook imports successful!")
    print()
    print("📋 Next steps for using in Jupyter:")
    print("1. Start Jupyter: jupyter lab or jupyter notebook")
    print("2. Select kernel: 'Python (ai-agent)'")
    print("3. Set your OpenRouter API key:")
    print("   OPENROUTER_API_KEY = getpass('Enter OpenRouter API Key: ')")
    print("   os.environ['OPENROUTER_API_KEY'] = OPENROUTER_API_KEY")
    print("4. Import and use:")
    print("   from deepseek_integration import ChatDeepSeek")
    print("   llm = ChatDeepSeek(model='deepseek/deepseek-chat:free', temperature=0.5)")
    
    return True

if __name__ == "__main__":
    success = test_notebook_imports()
    sys.exit(0 if success else 1)
