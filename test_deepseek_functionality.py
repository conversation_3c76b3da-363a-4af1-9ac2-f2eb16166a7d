#!/usr/bin/env python3
"""
Test script to verify DeepSeek integration functionality.
This script tests the integration without making actual API calls.
"""

import os
import sys
from unittest.mock import Mock, patch
from langchain_core.messages import HumanMessage, SystemMessage


def test_deepseek_initialization():
    """Test DeepSeek model initialization with mock API key."""
    print("Testing DeepSeek initialization...")
    
    try:
        # Set mock API key
        os.environ['OPENROUTER_API_KEY'] = 'test-key-123'
        
        from deepseek_integration import ChatDeepSeek
        
        # Initialize the model
        llm = ChatDeepSeek(
            model="deepseek/deepseek-chat:free",
            temperature=0.5,
            site_url="https://example.com",
            site_name="Test App"
        )
        
        print("✓ ChatDeepSeek initialized successfully")
        print(f"✓ Model: {llm.model}")
        print(f"✓ Temperature: {llm.temperature}")
        print(f"✓ Base URL: {llm.base_url}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to initialize DeepSeek: {e}")
        return False
    finally:
        # Clean up
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']


def test_message_conversion():
    """Test message conversion functionality."""
    print("\nTesting message conversion...")
    
    try:
        os.environ['OPENROUTER_API_KEY'] = 'test-key-123'
        
        from deepseek_integration import ChatDeepSeek
        
        llm = ChatDeepSeek()
        
        # Test message conversion
        human_msg = HumanMessage(content="Hello, world!")
        system_msg = SystemMessage(content="You are a helpful assistant.")
        
        human_dict = llm._convert_message_to_dict(human_msg)
        system_dict = llm._convert_message_to_dict(system_msg)
        
        assert human_dict == {"role": "user", "content": "Hello, world!"}
        assert system_dict == {"role": "system", "content": "You are a helpful assistant."}
        
        print("✓ Message conversion working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Message conversion failed: {e}")
        return False
    finally:
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']


def test_structured_output_setup():
    """Test structured output wrapper setup."""
    print("\nTesting structured output setup...")
    
    try:
        os.environ['OPENROUTER_API_KEY'] = 'test-key-123'
        
        from deepseek_integration import ChatDeepSeek
        from pydantic import BaseModel, Field
        from typing import List
        
        # Define a test schema
        class TestSchema(BaseModel):
            message: str = Field(description="A test message")
            count: int = Field(description="A test count")
        
        llm = ChatDeepSeek()
        structured_llm = llm.with_structured_output(TestSchema)
        
        print("✓ Structured output wrapper created successfully")
        print(f"✓ Schema: {structured_llm.schema}")
        
        return True
        
    except Exception as e:
        print(f"✗ Structured output setup failed: {e}")
        return False
    finally:
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']


def test_compatibility_alias():
    """Test that the compatibility alias works."""
    print("\nTesting compatibility alias...")
    
    try:
        os.environ['OPENROUTER_API_KEY'] = 'test-key-123'
        
        from deepseek_integration import ChatGoogleGenerativeAI
        
        # This should actually be ChatDeepSeek
        llm = ChatGoogleGenerativeAI(model="deepseek/deepseek-chat:free")
        
        print("✓ Compatibility alias working")
        print(f"✓ Model type: {type(llm).__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ Compatibility alias failed: {e}")
        return False
    finally:
        if 'OPENROUTER_API_KEY' in os.environ:
            del os.environ['OPENROUTER_API_KEY']


def main():
    """Run all functionality tests."""
    print("🧪 Testing DeepSeek Integration Functionality")
    print("=" * 60)
    
    tests = [
        test_deepseek_initialization,
        test_message_conversion,
        test_structured_output_setup,
        test_compatibility_alias
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All functionality tests passed! DeepSeek integration is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the integration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
