#!/usr/bin/env python3
"""
DeepSeek integration using OpenRouter API.
This module provides a ChatGoogleGenerativeAI-compatible interface for DeepSeek via OpenRouter.
"""

import os
from typing import Any, Dict, List, Optional, Union
from openai import OpenAI
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.outputs import Chat<PERSON><PERSON>ult, ChatGeneration
from pydantic import BaseModel, Field


class ChatDeepSeek(BaseChatModel):
    """
    DeepSeek chat model using OpenRouter API.
    Provides compatibility with ChatGoogleGenerativeAI interface.
    """

    model: str = Field(default="deepseek/deepseek-chat:free")
    temperature: float = Field(default=0.0)
    api_key: Optional[str] = Field(default=None)
    base_url: str = Field(default="https://openrouter.ai/api/v1")
    site_url: Optional[str] = Field(default=None)
    site_name: Optional[str] = Field(default=None)
    client: Optional[Any] = Field(default=None, exclude=True)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Get API key from environment if not provided
        if not self.api_key:
            self.api_key = os.getenv('OPENROUTER_API_KEY')
            if not self.api_key:
                raise ValueError("OPENROUTER_API_KEY environment variable must be set")
        
        # Initialize OpenAI client with OpenRouter configuration
        extra_headers = {}
        if self.site_url:
            extra_headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            extra_headers["X-Title"] = self.site_name
            
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
            default_headers=extra_headers if extra_headers else None
        )
    
    def _convert_message_to_dict(self, message: BaseMessage) -> Dict[str, str]:
        """Convert LangChain message to OpenAI format."""
        if isinstance(message, HumanMessage):
            return {"role": "user", "content": message.content}
        elif isinstance(message, SystemMessage):
            return {"role": "system", "content": message.content}
        elif isinstance(message, AIMessage):
            return {"role": "assistant", "content": message.content}
        else:
            # Default to user role for unknown message types
            return {"role": "user", "content": str(message.content)}
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate chat completion using DeepSeek via OpenRouter."""
        
        # Convert messages to OpenAI format
        openai_messages = [self._convert_message_to_dict(msg) for msg in messages]
        
        # Prepare request parameters
        request_params = {
            "model": self.model,
            "messages": openai_messages,
            "temperature": self.temperature,
        }
        
        # Add stop sequences if provided
        if stop:
            request_params["stop"] = stop
            
        # Add any additional parameters
        request_params.update(kwargs)
        
        try:
            # Make API call
            response = self.client.chat.completions.create(**request_params)
            
            # Extract content from response
            content = response.choices[0].message.content
            
            # Create ChatGeneration object
            generation = ChatGeneration(
                message=AIMessage(content=content),
                generation_info={
                    "model": response.model,
                    "usage": response.usage.dict() if response.usage else None,
                    "finish_reason": response.choices[0].finish_reason,
                }
            )
            
            return ChatResult(generations=[generation])
            
        except Exception as e:
            raise RuntimeError(f"Error calling DeepSeek via OpenRouter: {str(e)}")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[Any] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Async version of _generate."""
        # For now, just call the sync version
        # In a production environment, you'd want to use the async OpenAI client
        return self._generate(messages, stop, run_manager, **kwargs)
    
    def with_structured_output(self, schema: Any) -> "StructuredDeepSeek":
        """Return a structured output version of this model."""
        return StructuredDeepSeek(
            base_model=self,
            schema=schema
        )
    
    @property
    def _llm_type(self) -> str:
        """Return identifier of llm type."""
        return "deepseek_openrouter"


class StructuredDeepSeek:
    """
    Wrapper for structured output from DeepSeek.
    Provides compatibility with LangChain's structured output interface.
    """
    
    def __init__(self, base_model: ChatDeepSeek, schema: Any):
        self.base_model = base_model
        self.schema = schema
    
    def invoke(self, messages: List[BaseMessage], **kwargs) -> Any:
        """Invoke the model and parse output according to schema."""
        
        # Add instruction for structured output
        if messages and isinstance(messages[-1], HumanMessage):
            # Modify the last human message to request structured output
            original_content = messages[-1].content
            structured_instruction = f"""
{original_content}

Please respond with a JSON object that matches this schema:
{self._get_schema_description()}

Your response must be valid JSON that can be parsed directly.
"""
            messages[-1] = HumanMessage(content=structured_instruction)
        
        # Get response from base model
        result = self.base_model._generate(messages, **kwargs)
        content = result.generations[0].message.content
        
        # Try to parse as JSON and create schema object
        try:
            import json
            parsed_data = json.loads(content)
            
            # Create instance of schema class
            if hasattr(self.schema, 'parse_obj'):
                # Pydantic v1 style
                return self.schema.parse_obj(parsed_data)
            elif hasattr(self.schema, 'model_validate'):
                # Pydantic v2 style
                return self.schema.model_validate(parsed_data)
            else:
                # Fallback: try to instantiate directly
                return self.schema(**parsed_data)
                
        except (json.JSONDecodeError, Exception) as e:
            # If parsing fails, try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                try:
                    parsed_data = json.loads(json_match.group())
                    if hasattr(self.schema, 'parse_obj'):
                        return self.schema.parse_obj(parsed_data)
                    elif hasattr(self.schema, 'model_validate'):
                        return self.schema.model_validate(parsed_data)
                    else:
                        return self.schema(**parsed_data)
                except Exception:
                    pass
            
            # If all parsing attempts fail, raise an error
            raise ValueError(f"Failed to parse structured output: {content}. Error: {str(e)}")
    
    def _get_schema_description(self) -> str:
        """Get a description of the expected schema."""
        if hasattr(self.schema, 'schema'):
            # Pydantic model
            return str(self.schema.schema())
        elif hasattr(self.schema, '__annotations__'):
            # Dataclass or similar
            return str(self.schema.__annotations__)
        else:
            return str(self.schema)


# Compatibility alias
ChatGoogleGenerativeAI = ChatDeepSeek
