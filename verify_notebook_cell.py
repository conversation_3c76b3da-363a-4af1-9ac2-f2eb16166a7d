#!/usr/bin/env python3
"""
Verify that the exact code from notebook cell 15 works correctly.
This simulates the exact import that was failing in the notebook.
"""

import os
import sys

def main():
    print("🔬 Verifying Notebook Cell 15 Import")
    print("=" * 50)
    print(f"Using Python: {sys.executable}")
    print()
    
    # This is the exact import from the notebook cell that was failing
    print("Testing the exact import from notebook cell 15...")
    try:
        from deepseek_integration import ChatDeepSeek
        from langchain_core.messages import HumanMessage, SystemMessage
        
        print("✅ Imports successful!")
        print()
        
        # Test model initialization (the next line in the notebook)
        print("Testing model initialization...")
        
        # Set dummy API key for testing
        os.environ['OPENROUTER_API_KEY'] = 'test-key'
        
        llm = ChatDeepSeek(model="deepseek/deepseek-chat:free", temperature=0.5)
        
        print("✅ Model initialization successful!")
        print(f"   Model: {llm.model}")
        print(f"   Temperature: {llm.temperature}")
        print(f"   Base URL: {llm.base_url}")
        
        # Clean up
        del os.environ['OPENROUTER_API_KEY']
        
        print()
        print("🎉 Notebook cell 15 should now work correctly!")
        print()
        print("📝 Instructions for the notebook:")
        print("1. Make sure you're using the 'Python (ai-agent)' kernel")
        print("2. In the API key cell, use:")
        print("   OPENROUTER_API_KEY = getpass('Enter OpenRouter API Key: ')")
        print("3. In the environment setup cell, use:")
        print("   os.environ['OPENROUTER_API_KEY'] = OPENROUTER_API_KEY")
        print("4. The model initialization cell should now work!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print()
        print("🔧 Troubleshooting:")
        print("- Make sure you're using the 'Python (ai-agent)' kernel in Jupyter")
        print("- Restart the kernel if you just installed packages")
        print("- Check that deepseek_integration.py is in the same directory")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
