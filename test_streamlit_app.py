#!/usr/bin/env python3
"""
Test script for the Streamlit Research Agent Application.
This script tests the core components without requiring the full Streamlit interface.
"""

import os
import sys
from unittest.mock import Mock, patch

# Add the current directory to the path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")

    try:
        from streamlit_research_agent import (
            Section, ReportState, ReportStateInput, ReportStateOutput,
            SectionState, SectionOutputState
        )
        print("✅ Pydantic models imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Pydantic models: {e}")
        return False

    try:
        from streamlit_research_agent import (
            initialize_llm, initialize_tavily_client,
            generate_report_plan, generate_queries, search_web,
            write_section, format_sections, compile_final_report
        )
        print("✅ Agent functions imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import agent functions: {e}")
        return False

    try:
        from deepseek_integration import ChatDeepSeek
        print("✅ DeepSeek integration imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import DeepSeek integration: {e}")
        return False

    return True

def test_pydantic_models():
    """Test that Pydantic models work correctly."""
    print("\nTesting Pydantic models...")

    try:
        from streamlit_research_agent import Section, ReportState

        # Test Section model
        section = Section(
            name="Test Section",
            description="A test section",
            research=True
        )
        assert section.name == "Test Section"
        assert section.research == True
        print("✅ Section model works correctly")

        # Test ReportState model
        report_state = ReportState(
            topic="Test Topic",
            sections=[section]
        )
        assert report_state.topic == "Test Topic"
        assert len(report_state.sections) == 1
        print("✅ ReportState model works correctly")

        return True
    except Exception as e:
        print(f"❌ Pydantic model test failed: {e}")
        return False

def test_mock_agent_functions():
    """Test agent functions with mock dependencies."""
    print("\nTesting agent functions with mocks...")

    try:
        from streamlit_research_agent import (
            generate_report_plan, generate_queries, format_sections,
            Section, ReportState, SectionState
        )

        # Mock LLM
        mock_llm = Mock()
        mock_response = Mock()
        mock_response.sections = [
            {"name": "Introduction", "description": "Overview", "research": False},
            {"name": "Analysis", "description": "Deep analysis", "research": True}
        ]

        structured_llm = Mock()
        structured_llm.invoke.return_value = mock_response
        mock_llm.with_structured_output.return_value = structured_llm

        # Test generate_report_plan
        state = ReportState(topic="Test Topic")
        result = generate_report_plan(state, mock_llm)
        assert "sections" in result
        assert len(result["sections"]) == 2
        print("✅ generate_report_plan works with mock")

        # Test format_sections
        sections = [
            Section(name="Test", description="Test desc", research=True, content="Test content")
        ]
        formatted = format_sections(sections)
        assert "Test" in formatted
        assert "Test content" in formatted
        print("✅ format_sections works correctly")

        return True
    except Exception as e:
        print(f"❌ Agent function test failed: {e}")
        return False

def test_environment_setup():
    """Test environment setup requirements."""
    print("\nTesting environment setup...")

    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    else:
        print(f"✅ Python version {sys.version_info.major}.{sys.version_info.minor} is supported")

    # Check if required files exist
    required_files = [
        "streamlit_research_agent.py",
        "deepseek_integration.py",
        "requirements.txt"
    ]

    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} is missing")
            return False

    return True

def main():
    """Run all tests."""
    print("🧪 Testing Streamlit Research Agent Application")
    print("=" * 50)

    tests = [
        test_environment_setup,
        test_imports,
        test_pydantic_models,
        test_mock_agent_functions
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("\nTo run the application:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run the app: streamlit run streamlit_research_agent.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)