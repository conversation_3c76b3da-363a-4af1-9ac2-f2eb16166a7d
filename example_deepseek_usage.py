#!/usr/bin/env python3
"""
Example usage of DeepSeek integration via OpenRouter.
This demonstrates how to use the DeepSeek integration in place of the original Gemini integration.
"""

import os
from getpass import getpass
from deepseek_integration import ChatDeepSeek
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import List


class SearchQuery(BaseModel):
    """Example schema for structured output."""
    search_query: str = Field(description="Query for web search.")


class Queries(BaseModel):
    """Example schema for multiple queries."""
    queries: List[SearchQuery] = Field(description="List of web search queries.")


def setup_api_key():
    """Set up the OpenRouter API key."""
    if not os.getenv('OPENROUTER_API_KEY'):
        print("OpenRouter API key not found in environment.")
        api_key = getpass('Enter your OpenRouter API Key: ')
        os.environ['OPENROUTER_API_KEY'] = api_key
        return api_key
    return os.getenv('OPENROUTER_API_KEY')


def example_basic_chat():
    """Example of basic chat functionality."""
    print("🔹 Basic Chat Example")
    print("-" * 40)
    
    # Initialize the model
    llm = ChatDeepSeek(
        model="deepseek/deepseek-chat:free",
        temperature=0.5,
        site_url="https://example.com",  # Optional: for OpenRouter rankings
        site_name="DeepSeek Integration Example"  # Optional: for OpenRouter rankings
    )
    
    # Create messages
    messages = [
        SystemMessage(content="You are a helpful AI assistant."),
        HumanMessage(content="What is the capital of France?")
    ]
    
    print("Messages:")
    for msg in messages:
        print(f"  {msg.__class__.__name__}: {msg.content}")
    
    try:
        # Generate response
        result = llm._generate(messages)
        response = result.generations[0].message.content
        
        print(f"\nResponse: {response}")
        print("✅ Basic chat example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in basic chat: {e}")
        print("Note: This requires a valid OpenRouter API key and internet connection.")


def example_structured_output():
    """Example of structured output functionality."""
    print("\n🔹 Structured Output Example")
    print("-" * 40)
    
    # Initialize the model
    llm = ChatDeepSeek(
        model="deepseek/deepseek-chat:free",
        temperature=0.3
    )
    
    # Create structured output version
    structured_llm = llm.with_structured_output(Queries)
    
    # Create messages
    messages = [
        SystemMessage(content="Generate search queries for researching a topic."),
        HumanMessage(content="Generate 3 search queries about renewable energy technologies.")
    ]
    
    print("Messages:")
    for msg in messages:
        print(f"  {msg.__class__.__name__}: {msg.content}")
    
    try:
        # Generate structured response
        result = structured_llm.invoke(messages)
        
        print(f"\nStructured Response:")
        print(f"  Type: {type(result)}")
        print(f"  Queries: {[q.search_query for q in result.queries]}")
        print("✅ Structured output example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in structured output: {e}")
        print("Note: This requires a valid OpenRouter API key and internet connection.")


def example_compatibility():
    """Example showing compatibility with existing Gemini code."""
    print("\n🔹 Compatibility Example")
    print("-" * 40)
    
    # This shows how existing code using ChatGoogleGenerativeAI can work
    from deepseek_integration import ChatGoogleGenerativeAI  # This is actually ChatDeepSeek
    
    # Initialize using the old interface
    llm = ChatGoogleGenerativeAI(model="deepseek/deepseek-chat:free", temperature=0.5)
    
    print(f"Model type: {type(llm).__name__}")
    print(f"Model: {llm.model}")
    print(f"Temperature: {llm.temperature}")
    print("✅ Compatibility example completed successfully!")


def main():
    """Run all examples."""
    print("🚀 DeepSeek Integration Examples")
    print("=" * 50)
    
    # Setup API key
    api_key = setup_api_key()
    if not api_key:
        print("❌ No API key provided. Examples will show structure but may fail on actual API calls.")
    
    # Run examples
    example_basic_chat()
    example_structured_output()
    example_compatibility()
    
    print("\n" + "=" * 50)
    print("📝 Summary:")
    print("- Replace 'from langchain_google_genai import ChatGoogleGenerativeAI'")
    print("  with 'from deepseek_integration import ChatDeepSeek'")
    print("- Change model from 'gemini-*' to 'deepseek/deepseek-chat:free'")
    print("- Set OPENROUTER_API_KEY instead of GOOGLE_API_KEY")
    print("- All other functionality remains the same!")
    
    print("\n🎯 Your DeepSeek integration is ready to use!")


if __name__ == "__main__":
    main()
