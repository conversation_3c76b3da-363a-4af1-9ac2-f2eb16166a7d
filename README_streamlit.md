# AI Research Agent - Streamlit Application

A sophisticated Streamlit web application that replicates the Planning Agent for Deep Research & Structured Report Generation from the Jupyter notebook. This application uses LangGraph agents with parallel processing to generate comprehensive research reports.

## Features

- **🔐 Secure API Key Management**: Password-protected inputs for API keys with session state management
- **🤖 Advanced AI Integration**: Uses OpenRouter API with DeepSeek for high-quality report generation
- **🔍 Comprehensive Web Research**: Tavily API integration for deep web research
- **⚡ Parallel Processing**: LangGraph StateGraph architecture with parallel section processing
- **📊 Rich Report Generation**: Structured reports with proper markdown formatting
- **💾 Download Functionality**: Export reports as markdown files
- **🎯 Real-time Progress**: Live updates during report generation

## Architecture

The application faithfully replicates the notebook's architecture:

1. **Report Planning Agent**: Analyzes topics and creates structured outlines
2. **Section Builder Sub-Agent**: Handles individual section research and writing
3. **Parallel Processing**: Multiple sections researched simultaneously
4. **Final Compilation**: Combines all sections into a comprehensive report

## Prerequisites

- Python 3.8+
- Tavily API Key (for web search)
- OpenRouter API Key (for AI model access)

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Ensure the DeepSeek integration module is available**:
   The application requires `deepseek_integration.py` to be in the same directory.

## Usage

1. **Start the Streamlit application**:
   ```bash
   streamlit run streamlit_research_agent.py
   ```

2. **Configure API Keys**:
   - Enter your Tavily API key in the sidebar
   - Enter your OpenRouter API key in the sidebar
   - Optionally provide site URL and name for OpenRouter rankings

3. **Generate Reports**:
   - Enter your research topic in the main interface
   - Click "Generate Research Report"
   - Monitor progress in real-time
   - Download the generated report as a markdown file

## API Keys Setup

### Tavily API Key
1. Visit [Tavily](https://tavily.com/)
2. Sign up for an account
3. Get your API key from the dashboard

### OpenRouter API Key
1. Visit [OpenRouter](https://openrouter.ai/)
2. Create an account
3. Generate an API key
4. Optionally set up site information for rankings

## Report Generation Process

The application follows a sophisticated multi-step process:

1. **📋 Report Planning**: Analyzes your topic and creates a structured outline with 5-7 sections
2. **🔍 Parallel Research**: Conducts web research for each section simultaneously using Tavily
3. **✍️ Section Writing**: Generates detailed content for each section using AI
4. **📑 Final Compilation**: Combines all sections into a comprehensive, well-formatted report

## Security Features

- **Password-protected API key inputs**: Keys are never exposed in URLs or logs
- **Session state management**: Secure handling of sensitive information
- **No persistent storage**: API keys are only stored in session memory

## Technical Details

- **Framework**: Streamlit for web interface
- **AI Integration**: OpenRouter API with DeepSeek model
- **Search**: Tavily API for comprehensive web research
- **Architecture**: LangGraph StateGraph with parallel processing
- **State Management**: Pydantic schemas for type safety
- **Async Support**: Proper async handling for concurrent operations

## Troubleshooting

### Common Issues

1. **Missing API Keys**: Ensure both Tavily and OpenRouter API keys are provided
2. **Import Errors**: Verify all dependencies are installed via `pip install -r requirements.txt`
3. **DeepSeek Integration**: Ensure `deepseek_integration.py` is in the same directory
4. **Network Issues**: Check internet connection for API calls

### Error Messages

- **"❌ Tavily API key is required"**: Enter your Tavily API key in the sidebar
- **"❌ OpenRouter API key is required"**: Enter your OpenRouter API key in the sidebar
- **"❌ Please enter a research topic"**: Provide a research topic before generating

## Performance

- **Report Generation Time**: 2-5 minutes depending on topic complexity
- **Parallel Processing**: Multiple sections researched simultaneously for efficiency
- **Memory Usage**: Optimized for typical research report generation

## Comparison with Jupyter Notebook

This Streamlit application provides the exact same functionality as the original Jupyter notebook with these advantages:

- **User-friendly interface**: No coding knowledge required
- **Secure credential handling**: Password-protected API key inputs
- **Real-time progress**: Live updates during generation
- **Easy deployment**: Can be deployed to cloud platforms
- **Download functionality**: Built-in report export

## License

This project maintains the same license as the original notebook implementation.