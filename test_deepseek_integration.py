#!/usr/bin/env python3
"""
Test script to verify DeepSeek integration works correctly.
This script tests the basic functionality using DeepSeek via OpenRouter.
"""

import os
import sys
from getpass import getpass

def test_imports():
    """Test that all required imports work correctly."""
    print("Testing imports...")

    try:
        from deepseek_integration import ChatDeepSeek
        print("✓ deepseek_integration import successful")
    except ImportError as e:
        print(f"✗ Failed to import deepseek_integration: {e}")
        return False

    try:
        from langchain_core.messages import HumanMessage, SystemMessage
        print("✓ langchain_core.messages import successful")
    except ImportError as e:
        print(f"✗ Failed to import langchain_core.messages: {e}")
        return False

    try:
        from openai import OpenAI
        print("✓ openai import successful")
    except ImportError as e:
        print(f"✗ Failed to import openai: {e}")
        return False

    return True

def test_model_initialization():
    """Test that the DeepSeek model can be initialized."""
    print("\nTesting model initialization...")

    try:
        from deepseek_integration import ChatDeepSeek

        # Check if API key is set
        if not os.getenv('OPENROUTER_API_KEY'):
            print("⚠ OPENROUTER_API_KEY not set. Model initialization test skipped.")
            return True

        # Initialize the model
        llm = ChatDeepSeek(model="deepseek/deepseek-chat:free", temperature=0)
        print("✓ ChatDeepSeek model initialized successfully")
        return True

    except Exception as e:
        print(f"✗ Failed to initialize DeepSeek model: {e}")
        return False

def test_token_estimation():
    """Test the token estimation function that replaced tiktoken."""
    print("\nTesting token estimation function...")
    
    def estimate_tokens(text: str) -> int:
        return len(text) // 4
    
    test_text = "This is a test string for token estimation."
    estimated_tokens = estimate_tokens(test_text)
    
    print(f"✓ Token estimation working. Text: '{test_text}' -> ~{estimated_tokens} tokens")
    return True

def main():
    """Run all tests."""
    print("🧪 Testing DeepSeek Integration")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_model_initialization,
        test_token_estimation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DeepSeek integration appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the DeepSeek integration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
