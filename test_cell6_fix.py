#!/usr/bin/env python3
"""
Test script to verify that Cell 6 imports and SearchQuery class work correctly.
"""

def test_cell6_imports():
    """Test the imports and SearchQuery class from Cell 6."""
    print("Testing Cell 6 imports and SearchQuery class...")
    
    try:
        # Test the imports that were causing issues
        from langchain_community.utilities.tavily_search import TavilySearchAPIWrapper
        import asyncio
        from dataclasses import asdict, dataclass
        from typing import Dict, Any, List, Union
        
        print("✅ All imports successful")
        
        # Test the SearchQuery class definition
        @dataclass
        class SearchQuery:
            search_query: str

            def to_dict(self) -> Dict[str, Any]:
                return asdict(self)
        
        # Test creating and using SearchQuery
        query = SearchQuery(search_query="test query")
        query_dict = query.to_dict()
        
        print(f"✅ SearchQuery class works: {query_dict}")
        
        # Test the function signature that uses Union
        def test_function(search_queries: List[Union[str, SearchQuery]]) -> List[Dict]:
            return [{"test": "data"}]
        
        result = test_function([query, "string query"])
        print(f"✅ Function with Union type works: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Cell 6 Fix")
    print("=" * 30)
    
    if test_cell6_imports():
        print("=" * 30)
        print("🎉 Cell 6 fix verified! The NameError should be resolved.")
    else:
        print("=" * 30)
        print("❌ Cell 6 still has issues.")
