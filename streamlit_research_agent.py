#!/usr/bin/env python3
"""
Streamlit Research Agent Application
Based on the Planning Agent for Deep Research & Structured Report Generation notebook.
"""

import streamlit as st
import asyncio
import os
from typing import List, Dict, Any
from datetime import datetime

# Core imports from the notebook
from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.graph import StateGraph, START, END
from langgraph.constants import Send
from tavily import TavilyClient
from deepseek_integration import ChatDeepSeek

# State schemas from the notebook
class Section(BaseModel):
    name: str = Field(description="Name of the section")
    description: str = Field(description="Brief description of what the section should cover")
    research: bool = Field(description="Whether the section requires web research")
    content: str = Field(default="", description="Content of the section")

class ReportState(BaseModel):
    topic: str = Field(description="Main topic of the report")
    sections: List[Section] = Field(default_factory=list, description="List of sections in the report")
    completed_sections: List[Section] = Field(default_factory=list, description="List of completed sections")
    report_sections_from_research: str = Field(default="", description="Formatted sections from research")
    final_report: str = Field(default="", description="Final compiled report")

class ReportStateInput(BaseModel):
    topic: str = Field(description="Main topic of the report")

class ReportStateOutput(BaseModel):
    final_report: str = Field(description="Final compiled report")

class SectionState(BaseModel):
    section: Section = Field(description="Section to work on")
    search_queries: List[str] = Field(default_factory=list, description="Generated search queries")
    search_results: List[Dict[str, Any]] = Field(default_factory=list, description="Search results")
    source_str: str = Field(default="", description="Formatted source string")
    report_sections_from_research: str = Field(default="", description="Context from other sections")

class SectionOutputState(BaseModel):
    completed_sections: List[Section] = Field(description="Completed sections")

# Prompts from the notebook
REPORT_PLANNER_PROMPT = """You are an expert research analyst tasked with creating a comprehensive report structure.

Topic: {topic}

Create a detailed report structure with 5-7 main sections. Each section should:
1. Have a clear, descriptive name
2. Include a brief description of what it should cover
3. Indicate whether it requires web research (most sections should require research)

The report should follow this structure:
- Introduction (no research needed - will be written based on other sections)
- 4-6 research-heavy sections covering different aspects of the topic
- Conclusion (no research needed - will synthesize findings)

Focus on creating sections that will provide comprehensive coverage of the topic from multiple angles.
Ensure the sections are logical, well-organized, and will result in a thorough analysis.

Return your response as a JSON object with this exact structure:
{
  "sections": [
    {
      "name": "Section Name",
      "description": "Brief description of what this section covers",
      "research": true/false
    }
  ]
}"""

SEARCH_QUERY_PROMPT = """You are an expert research assistant. Generate 3-4 specific, targeted search queries for the following section:

Section: {section_name}
Description: {section_description}
Overall Topic: {topic}

Create search queries that will help gather comprehensive, current information for this section.
Make the queries specific and varied to capture different aspects and perspectives.
Focus on finding factual, authoritative sources.

Return your response as a JSON object:
{
  "search_queries": ["query1", "query2", "query3", "query4"]
}"""

SECTION_WRITER_PROMPT = """You are an expert technical writer crafting a section of a comprehensive research report.

Section Title: {section_title}
Section Topic: {section_topic}
Overall Report Topic: {topic}

Source Material:
{context}

Write a detailed, well-structured section that:

1. **Content Requirements:**
   - 300-500 words of substantive content
   - Uses concrete details and specific examples from the sources
   - Maintains an analytical, professional tone
   - Integrates information from multiple sources seamlessly

2. **Structure:**
   - Use ## for the section title (Markdown format)
   - Include a strong opening statement in bold that summarizes the key finding
   - Organize content with clear logical flow
   - Use bullet points or numbered lists when appropriate for clarity
   - End with a "Sources" section listing all referenced materials

3. **Source Integration:**
   - Reference specific data, statistics, and examples from the provided sources
   - Attribute information appropriately
   - Synthesize rather than just summarize sources

4. **Quality Standards:**
   - Every claim should be supported by source material
   - Use active voice and clear, direct language
   - Ensure content directly relates to the section topic
   - Maintain consistency with the overall report theme

Format the sources section as:
## Sources
• Source title : URL
• Source title : URL

Do not include word count or any preamble in your response.
If there are special characters in the text, such as the dollar symbol, ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5"""

FINAL_SECTION_WRITER_PROMPT = """You are an expert technical writer crafting a section that synthesizes information from the rest of the report.

Title for the section:
{section_title}

Topic for this section:
{section_topic}

Available report content of already completed sections:
{context}

1. Section-Specific Approach:

For Introduction:
- Use # for report title (Markdown format)
- 50-100 word limit
- Write in simple and clear language
- Focus on the core motivation for the report in 1-2 paragraphs
- Use a clear narrative arc to introduce the report
- Include NO structural elements (no lists or tables)
- No sources section needed

For Conclusion/Summary:
- Use ## for section title (Markdown format)
- 100-150 word limit
- For comparative reports:
    * Must include a focused comparison table using Markdown table syntax
    * Table should distill insights from the report
    * Keep table entries clear and concise
- For non-comparative reports:
    * Only use ONE structural element IF it helps distill the points made in the report:
    * Either a focused table comparing items present in the report (using Markdown table syntax)
    * Or a short list using proper Markdown list syntax:
      - Use `*` or `-` for unordered lists
      - Use `1.` for ordered lists
      - Ensure proper indentation and spacing
- End with specific next steps or implications
- No sources section needed

3. Writing Approach:
- Use concrete details over general statements
- Make every word count
- Focus on your single most important point

4. Quality Checks:
- For introduction: 50-100 word limit, # for report title, no structural elements, no sources section
- For conclusion: 100-150 word limit, ## for section title, only ONE structural element at most, no sources section
- Markdown format
- Do not include word count or any preamble in your response
- If there are special characters in the text, such as the dollar symbol,
  ensure they are escaped properly for correct rendering e.g $25.5 should become \\$25.5"""

# Agent node functions from the notebook
def initialize_llm(credentials):
    """Initialize the LLM with OpenRouter configuration."""
    return ChatDeepSeek(
        model="deepseek/deepseek-chat:free",
        temperature=0.0,
        api_key=credentials['openrouter_key'],
        site_url=credentials.get('site_url'),
        site_name=credentials.get('site_name')
    )

def initialize_tavily_client(api_key):
    """Initialize Tavily client for web search."""
    return TavilyClient(api_key=api_key)

def generate_report_plan(state: ReportState, llm) -> Dict[str, Any]:
    """Generate the overall report plan and structure."""
    print('--- Generating Report Plan ---')

    # Format the prompt
    system_instructions = REPORT_PLANNER_PROMPT.format(topic=state.topic)

    # Generate the plan
    user_instruction = "Create a comprehensive report structure for the given topic."

    # Use structured output to get JSON response
    structured_llm = llm.with_structured_output(
        schema=type('ReportPlan', (), {
            '__annotations__': {'sections': List[Dict[str, Any]]}
        })
    )

    try:
        response = structured_llm.invoke([
            SystemMessage(content=system_instructions),
            HumanMessage(content=user_instruction)
        ])

        # Convert to Section objects
        sections = []
        for section_data in response.sections:
            sections.append(Section(
                name=section_data['name'],
                description=section_data['description'],
                research=section_data['research']
            ))

        print('--- Generating Report Plan Completed ---')
        return {"sections": sections}

    except Exception as e:
        print(f"Error in generate_report_plan: {str(e)}")
        # Fallback to default structure
        default_sections = [
            Section(name="Introduction", description="Overview of the topic", research=False),
            Section(name="Background", description="Historical context and background", research=True),
            Section(name="Current State", description="Present situation and trends", research=True),
            Section(name="Analysis", description="Detailed analysis of key factors", research=True),
            Section(name="Conclusion", description="Summary and implications", research=False)
        ]
        return {"sections": default_sections}

def generate_queries(state: SectionState, llm) -> Dict[str, Any]:
    """Generate search queries for a section."""
    section = state.section
    print(f'--- Generating Search Queries for Section: {section.name} ---')

    # Format system instructions
    system_instructions = SEARCH_QUERY_PROMPT.format(
        section_name=section.name,
        section_description=section.description,
        topic=state.section.name  # Using section name as topic context
    )

    # Generate queries
    user_instruction = "Generate targeted search queries for comprehensive research."

    try:
        response = llm.invoke([
            SystemMessage(content=system_instructions),
            HumanMessage(content=user_instruction)
        ])

        # Parse the response to extract queries
        import json
        content = response.content

        # Try to extract JSON from response
        if '{' in content and '}' in content:
            start = content.find('{')
            end = content.rfind('}') + 1
            json_str = content[start:end]
            parsed = json.loads(json_str)
            queries = parsed.get('search_queries', [])
        else:
            # Fallback: split by lines and clean
            lines = content.strip().split('\n')
            queries = [line.strip().strip('"').strip("'") for line in lines if line.strip()][:4]

        print(f'--- Generating Search Queries for Section: {section.name} Completed ---')
        return {"search_queries": queries}

    except Exception as e:
        print(f"Error generating queries: {str(e)}")
        # Fallback queries
        fallback_queries = [
            f"{section.name} overview",
            f"{section.name} current trends",
            f"{section.name} analysis",
            f"{section.name} recent developments"
        ]
        return {"search_queries": fallback_queries}

def search_web(state: SectionState, tavily_client) -> Dict[str, Any]:
    """Search the web using Tavily."""
    print('--- Searching Web for Queries ---')

    search_results = []
    queries = state.search_queries

    for query in queries:
        try:
            # Search with Tavily
            results = tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=3
            )

            # Process results
            for result in results.get('results', []):
                search_results.append({
                    'title': result.get('title', ''),
                    'url': result.get('url', ''),
                    'content': result.get('content', ''),
                    'query': query
                })

        except Exception as e:
            print(f"Error searching for query '{query}': {str(e)}")
            continue

    # Format sources for the section writer
    source_str = ""
    for i, result in enumerate(search_results, 1):
        source_str += f"Source {i}: {result['title']}\n"
        source_str += f"URL: {result['url']}\n"
        source_str += f"Content: {result['content']}\n\n"

    print('--- Searching Web for Queries Completed ---')
    return {
        "search_results": search_results,
        "source_str": source_str
    }

def write_section(state: SectionState, llm) -> Dict[str, Any]:
    """Write a section of the report."""
    section = state.section
    source_str = state.source_str

    print(f'--- Writing Section : {section.name} ---')

    # Format system instructions
    system_instructions = SECTION_WRITER_PROMPT.format(
        section_title=section.name,
        section_topic=section.description,
        topic=section.name,  # Using section name as topic context
        context=source_str
    )

    # Generate section
    user_instruction = "Generate a report section based on the provided sources."
    section_content = llm.invoke([
        SystemMessage(content=system_instructions),
        HumanMessage(content=user_instruction)
    ])

    # Write content to the section object
    section.content = section_content.content

    print(f'--- Writing Section : {section.name} Completed ---')

    # Write the updated section to completed sections
    return {"completed_sections": [section]}

def format_sections(sections: List[Section]) -> str:
    """Format a list of report sections into a single text string."""
    formatted_str = ""
    for idx, section in enumerate(sections, 1):
        formatted_str += f"""
{'='*60}
Section {idx}: {section.name}
{'='*60}
Description:
{section.description}
Requires Research:
{section.research}

Content:
{section.content if section.content else '[Not yet written]'}

"""
    return formatted_str

def format_completed_sections(state: ReportState) -> Dict[str, Any]:
    """Gather completed sections from research and format them as context for writing the final sections."""
    print('--- Formatting Completed Sections ---')

    # List of completed sections
    completed_sections = state.completed_sections

    # Format completed section to str to use as context for final sections
    completed_report_sections = format_sections(completed_sections)

    print('--- Formatting Completed Sections is Done ---')

    return {"report_sections_from_research": completed_report_sections}

def write_final_sections(state: SectionState, llm) -> Dict[str, Any]:
    """Write the final sections of the report, which do not require web search and use the completed sections as context."""
    section = state.section
    completed_report_sections = state.report_sections_from_research

    print(f'--- Writing Final Section: {section.name} ---')

    # Format system instructions
    system_instructions = FINAL_SECTION_WRITER_PROMPT.format(
        section_title=section.name,
        section_topic=section.description,
        context=completed_report_sections
    )

    # Generate section
    user_instruction = "Craft a report section based on the provided sources."
    section_content = llm.invoke([
        SystemMessage(content=system_instructions),
        HumanMessage(content=user_instruction)
    ])

    # Write content to section
    section.content = section_content.content

    print(f'--- Writing Final Section: {section.name} Completed ---')

    # Write the updated section to completed sections
    return {"completed_sections": [section]}

def compile_final_report(state: ReportState) -> Dict[str, Any]:
    """Compile the final report."""
    sections = state.sections
    completed_sections = {s.name: s.content for s in state.completed_sections}

    print('--- Compiling Final Report ---')

    # Update sections with completed content while maintaining original order
    for section in sections:
        section.content = completed_sections[section.name]

    # Compile final report
    all_sections = "\n\n".join([s.content for s in sections])
    # Escape unescaped $ symbols to display properly in Markdown
    formatted_sections = all_sections.replace("\\$", "TEMP_PLACEHOLDER")  # Temporarily mark already escaped $
    formatted_sections = formatted_sections.replace("$", "\\$")  # Escape all $
    formatted_sections = formatted_sections.replace("TEMP_PLACEHOLDER", "\\$")  # Restore originally escaped $

    print('--- Compiling Final Report Done ---')

    return {"final_report": formatted_sections}

# Parallelization functions
def parallelize_section_writing(state: ReportState):
    """This is the "map" step when we kick off web research for some sections of the report in parallel and then write the section."""
    # Kick off section writing in parallel via Send() API for any sections that require research
    return [
        Send("section_builder_with_web_search", {"section": s})
        for s in state.sections
        if s.research
    ]

def parallelize_final_section_writing(state: ReportState):
    """Write any final sections using the Send API to parallelize the process."""
    # Kick off section writing in parallel via Send() API for any sections that do not require research
    return [
        Send("write_final_sections", {
            "section": s,
            "report_sections_from_research": state.report_sections_from_research
        })
        for s in state.sections
        if not s.research
    ]

def build_section_builder_subagent(llm, tavily_client):
    """Build the section builder sub-agent."""
    # Create wrapper functions that include the required dependencies
    def generate_queries_wrapper(state: SectionState):
        return generate_queries(state, llm)

    def search_web_wrapper(state: SectionState):
        return search_web(state, tavily_client)

    def write_section_wrapper(state: SectionState):
        return write_section(state, llm)

    # Add nodes and edges
    section_builder = StateGraph(SectionState, output=SectionOutputState)
    section_builder.add_node("generate_queries", generate_queries_wrapper)
    section_builder.add_node("search_web", search_web_wrapper)
    section_builder.add_node("write_section", write_section_wrapper)

    section_builder.add_edge(START, "generate_queries")
    section_builder.add_edge("generate_queries", "search_web")
    section_builder.add_edge("search_web", "write_section")
    section_builder.add_edge("write_section", END)

    return section_builder.compile()

def build_reporter_agent(llm, tavily_client):
    """Build the main reporter agent."""
    # Build the section builder sub-agent
    section_builder_subagent = build_section_builder_subagent(llm, tavily_client)

    # Create wrapper functions
    def generate_report_plan_wrapper(state: ReportState):
        return generate_report_plan(state, llm)

    def write_final_sections_wrapper(state: SectionState):
        return write_final_sections(state, llm)

    # Build main agent
    builder = StateGraph(ReportState, input=ReportStateInput, output=ReportStateOutput)

    builder.add_node("generate_report_plan", generate_report_plan_wrapper)
    builder.add_node("section_builder_with_web_search", section_builder_subagent)
    builder.add_node("format_completed_sections", format_completed_sections)
    builder.add_node("write_final_sections", write_final_sections_wrapper)
    builder.add_node("compile_final_report", compile_final_report)

    builder.add_edge(START, "generate_report_plan")
    builder.add_conditional_edges("generate_report_plan",
                                  parallelize_section_writing,
                                  ["section_builder_with_web_search"])
    builder.add_edge("section_builder_with_web_search", "format_completed_sections")
    builder.add_conditional_edges("format_completed_sections",
                                  parallelize_final_section_writing,
                                  ["write_final_sections"])
    builder.add_edge("write_final_sections", "compile_final_report")
    builder.add_edge("compile_final_report", END)

    return builder.compile()

async def call_planner_agent(agent, topic: str, progress_callback=None):
    """Call the planner agent and generate a report."""
    config = {"recursion_limit": 50}

    events = agent.astream(
        {'topic': topic},
        config,
        stream_mode="values",
    )

    final_report = ""
    async for event in events:
        for k, v in event.items():
            if progress_callback:
                progress_callback(k, v)
            if k == 'final_report':
                final_report = v
                break

    return final_report

# Initialize session state
def init_session_state():
    """Initialize Streamlit session state variables."""
    if 'report_generated' not in st.session_state:
        st.session_state.report_generated = False
    if 'final_report' not in st.session_state:
        st.session_state.final_report = ""
    if 'generation_in_progress' not in st.session_state:
        st.session_state.generation_in_progress = False

def setup_page():
    """Configure Streamlit page settings."""
    st.set_page_config(
        page_title="AI Research Agent",
        page_icon="🔬",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("🔬 AI Research Agent")
    st.markdown("Generate comprehensive research reports using advanced AI planning agents")

def get_api_credentials():
    """Get API credentials from user input with secure handling."""
    st.sidebar.header("🔐 API Configuration")

    # Tavily API Key
    tavily_key = st.sidebar.text_input(
        "Tavily API Key",
        type="password",
        help="Enter your Tavily API key for web search functionality",
        key="tavily_api_key"
    )

    # OpenRouter API Key
    openrouter_key = st.sidebar.text_input(
        "OpenRouter API Key",
        type="password",
        help="Enter your OpenRouter API key for AI model access",
        key="openrouter_api_key"
    )

    # Optional: Site information for OpenRouter rankings
    st.sidebar.subheader("Optional: Site Information")
    site_url = st.sidebar.text_input(
        "Site URL",
        help="Your site URL for OpenRouter rankings (optional)",
        key="site_url"
    )

    site_name = st.sidebar.text_input(
        "Site Name",
        help="Your site name for OpenRouter rankings (optional)",
        key="site_name"
    )

    return {
        'tavily_key': tavily_key,
        'openrouter_key': openrouter_key,
        'site_url': site_url if site_url else None,
        'site_name': site_name if site_name else None
    }

def validate_credentials(credentials):
    """Validate that required API credentials are provided."""
    if not credentials['tavily_key']:
        st.error("❌ Tavily API key is required")
        st.info("💡 Get your Tavily API key from: https://tavily.com/")
        return False
    if not credentials['openrouter_key']:
        st.error("❌ OpenRouter API key is required")
        st.info("💡 Get your OpenRouter API key from: https://openrouter.ai/")
        return False

    # Basic format validation
    if len(credentials['tavily_key']) < 10:
        st.error("❌ Tavily API key appears to be invalid (too short)")
        return False
    if len(credentials['openrouter_key']) < 10:
        st.error("❌ OpenRouter API key appears to be invalid (too short)")
        return False

    return True

def get_research_topic():
    """Get research topic from user input."""
    st.header("📝 Research Topic")

    topic = st.text_area(
        "Enter your research topic:",
        height=100,
        placeholder="e.g., Detailed analysis of the impact of artificial intelligence on healthcare systems",
        help="Provide a clear and specific research topic for comprehensive analysis"
    )

    # Topic validation
    if topic and len(topic.strip()) < 10:
        st.warning("⚠️ Please provide a more detailed research topic (at least 10 characters)")

    return topic.strip() if topic else ""

def create_download_button(report_content, filename=None):
    """Create a download button for the generated report."""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"research_report_{timestamp}.md"

    # Create download button
    st.download_button(
        label="📥 Download Report (Markdown)",
        data=report_content,
        file_name=filename,
        mime="text/markdown",
        help="Download the generated report as a Markdown file"
    )

def display_progress_info():
    """Display information about the report generation process."""
    with st.expander("ℹ️ About the Report Generation Process"):
        st.markdown("""
        This AI Research Agent uses a sophisticated multi-step process:

        1. **📋 Report Planning**: Analyzes your topic and creates a structured outline
        2. **🔍 Parallel Research**: Conducts web research for each section simultaneously
        3. **✍️ Section Writing**: Generates detailed content for each section
        4. **📑 Final Compilation**: Combines all sections into a comprehensive report

        The process typically takes 2-5 minutes depending on topic complexity.
        """)

def main():
    """Main Streamlit application."""
    init_session_state()
    setup_page()

    # Get API credentials
    credentials = get_api_credentials()

    # Get research topic
    topic = get_research_topic()

    # Display process information
    display_progress_info()

    # Generate report button
    _, col2, _ = st.columns([1, 2, 1])
    with col2:
        generate_button = st.button(
            "🚀 Generate Research Report",
            disabled=st.session_state.generation_in_progress,
            use_container_width=True,
            type="primary"
        )

    # Handle report generation
    if generate_button:
        if not validate_credentials(credentials):
            st.stop()

        if not topic:
            st.error("❌ Please enter a research topic")
            st.stop()

        if len(topic) < 10:
            st.error("❌ Please provide a more detailed research topic (at least 10 characters)")
            st.stop()

        # Set environment variables for the agent
        os.environ['TAVILY_API_KEY'] = credentials['tavily_key']
        os.environ['OPENROUTER_API_KEY'] = credentials['openrouter_key']

        st.session_state.generation_in_progress = True

        # Generate report
        with st.spinner("🔄 Generating comprehensive research report..."):
            try:
                # Initialize AI services
                llm = initialize_llm(credentials)
                tavily_client = initialize_tavily_client(credentials['tavily_key'])

                # Build the reporter agent
                reporter_agent = build_reporter_agent(llm, tavily_client)

                # Create progress display
                progress_text = st.empty()

                def progress_callback(key, value):
                    """Callback to update progress in Streamlit."""
                    if key in ['sections', 'completed_sections', 'final_report']:
                        if key == 'sections':
                            progress_text.text(f"📋 Generated report plan with {len(value)} sections")
                        elif key == 'completed_sections':
                            progress_text.text(f"✅ Completed section: {value[-1].name if value else 'Unknown'}")
                        elif key == 'final_report':
                            progress_text.text("🎉 Report compilation complete!")

                # Generate the report
                final_report = asyncio.run(call_planner_agent(reporter_agent, topic, progress_callback))

                # Store in session state
                st.session_state.final_report = final_report
                st.session_state.report_generated = True
                st.session_state.generation_in_progress = False

                # Success message
                st.success("✅ Research report generated successfully!")
                st.rerun()

            except Exception as e:
                error_msg = str(e)
                st.error(f"❌ Error generating report: {error_msg}")

                # Provide specific guidance based on error type
                if "api" in error_msg.lower() or "key" in error_msg.lower():
                    st.info("💡 This appears to be an API key issue. Please check your credentials.")
                elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                    st.info("💡 This appears to be a network issue. Please check your internet connection.")
                elif "timeout" in error_msg.lower():
                    st.info("💡 The request timed out. Please try again with a simpler topic.")
                else:
                    st.info("💡 Please try again. If the issue persists, try with a different research topic.")

                st.session_state.generation_in_progress = False

    # Display generated report
    if st.session_state.report_generated and st.session_state.final_report:
        st.header("📊 Generated Research Report")

        # Display report
        st.markdown(st.session_state.final_report)

        # Download button
        st.header("💾 Download Report")
        create_download_button(st.session_state.final_report)

if __name__ == "__main__":
    main()