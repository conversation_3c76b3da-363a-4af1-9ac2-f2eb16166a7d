aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
babel==2.17.0
beautifulsoup4==4.13.4
bleach==6.2.0
cachetools==5.5.2
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
comm==0.2.2
dataclasses-json==0.6.7
debugpy==1.8.14
decorator==5.2.1
defusedxml==0.7.1
distro==1.9.0
executing==2.2.0
fastjsonschema==2.21.1
filetype==1.2.0
fqdn==1.5.1
frozenlist==1.7.0
google-ai-generativelanguage==0.6.18
google-api-core==2.25.1
google-api-python-client==2.173.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
googleapis-common-protos==1.70.0
grpcio==1.73.0
grpcio-status==1.71.0
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.1
idna==3.10
ipykernel==6.29.5
ipython==9.3.0
ipython_pygments_lexers==1.1.1
ipywidgets==8.1.7
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.10.0
json5==0.12.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.8.1
jupyter_server==2.16.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.3
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
langchain==0.3.14
langchain-community==0.3.14
langchain-core==0.3.63
langchain-google-genai==2.1.5
langchain-text-splitters==0.3.8
langgraph==0.2.64
langgraph-checkpoint==2.1.0
langgraph-sdk==0.1.70
langsmith==0.2.11
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.1.3
multidict==6.5.1
mypy_extensions==1.1.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
notebook==7.4.3
notebook_shim==0.2.4
numpy==2.3.1
openai==1.92.2
orjson==3.10.18
ormsgpack==1.10.0
overrides==7.7.0
packaging==24.2
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
platformdirs==4.3.8
prometheus_client==0.22.1
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
Pygments==2.19.2
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
PyYAML==6.0.2
pyzmq==27.0.0
referencing==0.36.2
requests==2.32.4
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
Send2Trash==1.8.3
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
SQLAlchemy==2.0.41
stack-data==0.6.3
tenacity==9.1.2
terminado==0.18.1
tinycss2==1.4.0
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
types-python-dateutil==2.9.0.20250516
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
uri-template==1.3.0
uritemplate==4.2.0
urllib3==2.5.0
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
widgetsnbextension==4.0.14
yarl==1.20.1
zstandard==0.23.0
